import React, { useState } from "react";
import { View } from "react-native";
import { useTranslation } from "react-i18next";
import { router } from "expo-router";
import Layout from "@/components/Layout";
import HeaderExperiencesFilter from "@/components/HeaderExperiencesFilter";
import ExperiencesTypeTabs from "@/features/experience/components/ExperiencesTypeTabs";
import ExperienceList from "@/features/experience/components/ExperienceList";
import ExperienceCard from "@/features/experience/components/ExperienceCard";
import { ExperienceTypeKey, ExperienceType } from "@/features/experience/model";
import { useGetExperiencesQuery } from "@/features/experience/hooks";

export default function ExperiencesScreen() {
  const { t } = useTranslation();
  const [selectedType, setSelectedType] = useState<ExperienceTypeKey>("ALL");
  const [searchValue, setSearchValue] = useState("");
  const {
    data: experiences,
    isFetching: isLoadingExperiences,
    isError,
    refetch,
  } = useGetExperiencesQuery();

  const handleSearch = (text: string) => {
    setSearchValue(text);
    // TODO: Implement search functionality
  };

  const handleFilterPress = () => {
    // TODO: Implement filter functionality
  };

  const handleTypeSelect = (type: ExperienceTypeKey) => {
    setSelectedType(type);
    // TODO: Implement type filtering
  };

  const handleExperiencePress = (experience: ExperienceType) => {
    router.push({
      pathname: "/experiences/[id]",
      params: { id: experience._id },
    });
  };

  return (
    <>
      <Layout
        title={{ text: t("common.experiences") }}
        showSearch
        noScroll
        noMargin
        noPadding
        headerFilter={
          <View className="gap-5">
            <HeaderExperiencesFilter
              onSearch={handleSearch}
              onFilterPress={handleFilterPress}
              searchValue={searchValue}
              searchPlaceholder={t("common.search")}
            />
            <ExperiencesTypeTabs
              selectedType={selectedType}
              onTypeSelect={handleTypeSelect}
            />
          </View>
        }
      >
        <ExperienceList
          experiences={experiences}
          isLoading={isLoadingExperiences}
          isError={isError}
          onRefresh={refetch}
        >
          {({ item }) => {
            return (
              <ExperienceCard
                experience={item}
                onPress={() => handleExperiencePress(item)}
              />
            );
          }}
        </ExperienceList>
      </Layout>
    </>
  );
}
