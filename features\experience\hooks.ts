import { API_URLS } from "@/config/api";
import useGetQuery from "@/hooks/useGetQuery";
import { z } from "zod";
import { experienceSchema } from "./model";
import { handleErrors } from "@/lib/errors";

export const useGetExperiencesQuery = () => {
  const res = useGetQuery({
    apiUrl: API_URLS.experiences,
    queryKey: "experiences",
    enabled: true,
    initialData: [],
  });

  return { ...res, data: z.array(experienceSchema).parse(res.data) };
};

export const useGetExperienceByIdQuery = ({ id }: { id: string }) => {
  const res = useGetQuery({
    apiUrl: API_URLS.experienceById(id),
    queryKey: `experience-${id}`,
    enabled: true,
    initialData: undefined,
  });

  console.log({ res });

  const schema = experienceSchema.safeParse(res.data);

  console.log({ error: schema.error });

  handleErrors({
    error: schema.error,
    message: "Erro ao buscar experiência",
  });

  return { ...res, data: schema.data, isError: !schema.success };
};
